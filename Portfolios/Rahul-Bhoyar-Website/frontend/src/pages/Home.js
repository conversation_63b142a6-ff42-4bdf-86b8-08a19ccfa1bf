import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>er, <PERSON>, <PERSON>, Button } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import {
  FaArrowRight, FaCode, FaChartBar, FaLaptopCode,
  FaDatabase, FaRobot, FaGithub, FaLinkedin, FaTwitter,
  FaAngleDown, FaStar, FaDocker, FaMedium, FaFlask
} from 'react-icons/fa';
import { SiKaggle, SiGooglescholar, SiFigma, SiResearchgate } from 'react-icons/si';
import './Home.css';

const Home = () => {
  const heroImageRef = useRef(null);
  const heroContentRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);
  const servicesRef = useRef(null);
  const statsRef = useRef(null);


  // Animation for hero section elements
  useEffect(() => {
    setIsVisible(true);
  }, []);

  // Counter animation for stats
  useEffect(() => {
    const animateCounter = (element, target, duration) => {
      if (!element) return;

      let start = 0;
      const increment = target > 0 ? 1 : 0;
      const stepTime = Math.abs(Math.floor(duration / target));

      const timer = setInterval(() => {
        start += increment;
        element.textContent = start + '+';
        if (start >= target) {
          element.textContent = target + '+';
          clearInterval(timer);
        }
      }, stepTime);
    };

    const handleStatsAnimation = () => {
      const statsSection = statsRef.current;
      if (statsSection && statsSection.classList.contains('animate-in')) {
        const counters = statsSection.querySelectorAll('.counter');
        counters.forEach(counter => {
          const target = parseInt(counter.textContent);
          animateCounter(counter, target, 2000);
        });
      }
    };

    // Parallax scrolling effect and section animations
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      if (heroImageRef.current) {
        heroImageRef.current.style.transform = `translateY(${scrollPosition * 0.2}px)`;
      }
      if (heroContentRef.current) {
        heroContentRef.current.style.transform = `translateY(${scrollPosition * 0.1}px)`;
      }

      // Add animation for services and stats sections when they come into view
      const servicesSection = servicesRef.current;
      const statsSection = statsRef.current;

      if (servicesSection) {
        const servicesSectionTop = servicesSection.getBoundingClientRect().top;
        if (servicesSectionTop < window.innerHeight * 0.75) {
          servicesSection.classList.add('animate-in');
        }
      }

      if (statsSection) {
        const statsSectionTop = statsSection.getBoundingClientRect().top;
        if (statsSectionTop < window.innerHeight * 0.75 && !statsSection.classList.contains('animate-in')) {
          statsSection.classList.add('animate-in');
          // Start counter animation when stats section comes into view
          setTimeout(handleStatsAnimation, 500);
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="home-page">
      <section className="hero-section">
        <div className="hero-bg">
          <div className="hero-particles"></div>
        </div>
        <Container fluid className="hero-container">
          <Row className="align-items-center">
            <Col lg={6} className={`hero-content text-left ${isVisible ? 'fade-in' : ''}`} ref={heroContentRef}>
              <div className="hero-intro">
                <div className="badge-container">
                  <span className="badge badge-animate">Full Stack Developer</span>
                  <span className="badge badge-animate" style={{ animationDelay: '0.2s' }}>Data Scientist</span>
                  <span className="badge badge-animate" style={{ animationDelay: '0.4s' }}>AI Engineer</span>
                </div>
                <div className="hero-headline">
                  <h1 className="hero-greeting">
                    <span className="greeting-text">Transforming Ideas Into</span>
                    <span className="greeting-highlight">Digital Reality</span>
                  </h1>
                  <h1 className="hero-name text-gradient">Rahul Bhoyar</h1>
                  <div className="hero-subtitle">
                    <span className="subtitle-text">AI Engineer</span>
                    <span className="subtitle-separator">•</span>
                    <span className="subtitle-text">Full-Stack Developer</span>
                    <span className="subtitle-separator">•</span>
                    <span className="subtitle-text">Innovation Catalyst</span>
                  </div>
                </div>
                <div className="hero-tagline-container">
                  <p className="hero-tagline">
                    Architecting the future with <span className="highlight pulse">Agentic AI</span>,
                    <span className="highlight pulse" style={{ animationDelay: '0.5s' }}>intelligent systems</span>, and
                    <span className="highlight pulse" style={{ animationDelay: '1s' }}>cutting-edge technology</span>
                    that bridges human creativity with machine intelligence.
                  </p>
                </div>
              </div>
              <div className="hero-buttons">
                <Button as={Link} to="/projects" className="cta-button primary-cta">
                  <span className="cta-text">Explore Projects</span>
                  <span className="cta-icon"><FaArrowRight /></span>
                </Button>
                <Button as={Link} to="/contact" className="cta-button secondary-cta">
                  <span className="cta-text">Get In Touch</span>
                </Button>
              </div>
              <div className="social-links-hero">
                <a href="https://github.com/rahulbhoyar1995" target="_blank" rel="noopener noreferrer" className="social-link github" title="GitHub">
                  <FaGithub />
                </a>
                <a href="https://www.linkedin.com/in/rahul-bhoyar-mba-1a04a7215/" target="_blank" rel="noopener noreferrer" className="social-link linkedin" title="LinkedIn">
                  <FaLinkedin />
                </a>
                <a href="https://hub.docker.com/repositories/rahulbhoyar" target="_blank" rel="noopener noreferrer" className="social-link twitter" title="DockerHub">
                  <FaDocker />
                </a>
                <a href="https://huggingface.co/rahul-bhoyar-1995" target="_blank" rel="noopener noreferrer" className="social-link huggingface" title="Hugging Face">
                  <FaFlask />
                </a>
                <a href="https://medium.com/@rahulbhoyaroffice" target="_blank" rel="noopener noreferrer" className="social-link medium" title="Medium">
                  <FaMedium />
                </a>
                <a href="https://scholar.google.com/citations?user=youruserid" target="_blank" rel="noopener noreferrer" className="social-link google-scholar" title="Google Scholar">
                  <SiGooglescholar />
                </a>
                <a href="https://www.kaggle.com/rrb8695" target="_blank" rel="noopener noreferrer" className="social-link kaggle" title="Kaggle">
                  <SiKaggle />
                </a>
                <a href="https://www.researchgate.net/profile/Rahul-Bhoyar-3?ev=hdr_xprf" target="_blank" rel="noopener noreferrer" className="social-link researchgate" title="Research Gate">
                  <SiResearchgate />
                </a>
              </div>
            </Col>
            <Col lg={6} className={`hero-image-col ${isVisible ? 'slide-in-right' : ''}`}>
              <div className="hero-image-container" ref={heroImageRef}>
                <img
                  src={`${process.env.PUBLIC_URL}/images/home/<USER>
                  alt="Rahul Bhoyar"
                  className="hero-image"
                />
                <div className="hero-image-overlay"></div>
              </div>
            </Col>
          </Row>
          <div className="scroll-indicator">
            <FaAngleDown className="scroll-icon bounce" />
          </div>
        </Container>
      </section>

      <section className="services-section" ref={servicesRef}>
        <Container>
          <div className="section-header">
            <h2 className="section-title text-center">Expertise & Services</h2>
            <div className="section-title-underline"></div>
          </div>
          <Row>
            <Col md={4} className="mb-4 mb-md-0 service-col">
              <div className="service-card">
                <div className="service-icon-container">
                  <div className="service-icon">
                    <FaLaptopCode />
                  </div>
                </div>
                <h3 className="service-title">Frontend Development</h3>
                <p className="service-description">
                  Building stunning, responsive interfaces with React, leveraging modern frameworks and design systems for exceptional user experiences.
                </p>
                <Link to="/projects" className="service-link">
                  View projects <FaArrowRight className="ms-1" />
                </Link>
                <div className="service-card-bg"></div>
              </div>
            </Col>
            <Col md={4} className="mb-4 mb-md-0 service-col" style={{ animationDelay: '0.2s' }}>
              <div className="service-card">
                <div className="service-icon-container">
                  <div className="service-icon">
                    <FaDatabase />
                  </div>
                </div>
                <h3 className="service-title">Backend Engineering</h3>
                <p className="service-description">
                  Crafting robust APIs and microservices with FastAPI and Python, focusing on performance, security, and scalable architecture.
                </p>
                <Link to="/projects" className="service-link">
                  View projects <FaArrowRight className="ms-1" />
                </Link>
                <div className="service-card-bg"></div>
              </div>
            </Col>
            <Col md={4} className="service-col" style={{ animationDelay: '0.4s' }}>
              <div className="service-card">
                <div className="service-icon-container">
                  <div className="service-icon">
                    <FaRobot />
                  </div>
                </div>
                <h3 className="service-title">AI & ML Solutions</h3>
                <p className="service-description">
                  Implementing cutting-edge machine learning models and AI systems that transform data into actionable intelligence and business value.
                </p>
                <Link to="/projects" className="service-link">
                  View projects <FaArrowRight className="ms-1" />
                </Link>
                <div className="service-card-bg"></div>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      <section className="stats-section" ref={statsRef}>
        <div className="stats-bg"></div>
        <Container>
          <div className="section-header mb-4">
            <h2 className="section-title text-center">By The Numbers</h2>
            <div className="section-title-underline"></div>
          </div>
          <Row>
            <Col md={3} sm={6} className="stat-item">
              <div className="stat-card">
                <div className="stat-icon">
                  <FaCode />
                </div>
                <div className="stat-number counter">40+</div>
                <div className="stat-label">Projects</div>
              </div>
            </Col>
            <Col md={3} sm={6} className="stat-item">
              <div className="stat-card">
                <div className="stat-icon">
                  <FaLaptopCode />
                </div>
                <div className="stat-number counter">20+</div>
                <div className="stat-label">Tech Stack Mastery</div>
              </div>
            </Col>
            <Col md={3} sm={6} className="stat-item">
              <div className="stat-card">
                <div className="stat-icon">
                  <FaChartBar />
                </div>
                <div className="stat-number counter">8</div>
                <div className="stat-label">Years Experience</div>
              </div>
            </Col>
            <Col md={3} sm={6} className="stat-item">
              <div className="stat-card">
                <div className="stat-icon">
                  <FaStar />
                </div>
                <div className="stat-number counter">10+</div>
                <div className="stat-label">Satisfied Clients</div>
              </div>
            </Col>
          </Row>
        </Container>
      </section>
    </div>
  );
};

export default Home;

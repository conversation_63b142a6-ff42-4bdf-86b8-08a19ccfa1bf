import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Button, InputGroup, FormControl, Spinner } from 'react-bootstrap';
import { FaSearch, FaFilter, FaSortAmountDown, FaSortAmountUp } from 'react-icons/fa';
import axios from 'axios';
import ProjectCard from '../components/ProjectCard';
import './Projects.css';

const Projects = () => {
  const [projects, setProjects] = useState([]);
  const [filteredProjects, setFilteredProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [sortOrder, setSortOrder] = useState('desc'); // 'desc' for newest first

  // Sample categories - in a real app, these would be derived from the projects data
  const categories = ['All', 'Generative AI','Deep Learning','Natural Language Processing','Machine Learning','Data Science','Data Engineering','Cloud','Web Development', 'Backend Development', 'Mobile Apps', 'DevOps/MLOps','UI/UX Design'];

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        // Fetch projects from API
        const response = await axios.get('/api/projects');
        const projectsData = response.data;

        setProjects(projectsData);
        setFilteredProjects(projectsData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching projects:', err);
        setError('Failed to load projects. Please try again later.');
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  // Filter and sort projects when search term, category, or sort order changes
  useEffect(() => {
    let result = [...projects];

    // Filter by search term
    if (searchTerm) {
      result = result.filter(project =>
        project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.technologies.some(tech => tech.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filter by category
    if (selectedCategory !== 'All') {
      result = result.filter(project => project.category === selectedCategory);
    }

    // Sort by date
    result = result.sort((a, b) => {
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);
      return sortOrder === 'desc' ? dateB - dateA : dateA - dateB;
    });

    setFilteredProjects(result);
  }, [searchTerm, selectedCategory, sortOrder, projects]);

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleCategoryChange = (category) => {
    setSelectedCategory(category);
  };

  const toggleSortOrder = () => {
    setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc');
  };

  if (loading) {
    return (
      <Container className="py-5 mt-5">
        <div className="text-center">
          <Spinner animation="border" variant="primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <p className="mt-3">Loading projects...</p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="py-5 mt-5">
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      </Container>
    );
  }

  return (
    <section className="projects-section">
      <Container>
        <h2 className="section-title text-center">My Projects</h2>
        <div className="section-intro text-center mb-5">
          <p className="lead">
            A showcase of my technical work across various domains and technologies
          </p>
        </div>

        <div className="projects-filters mb-5">
          <Row className="align-items-center">
            <Col lg={6} md={12} className="mb-3 mb-lg-0">
              <InputGroup>
                <InputGroup.Text>
                  <FaSearch />
                </InputGroup.Text>
                <FormControl
                  placeholder="Search projects..."
                  value={searchTerm}
                  onChange={handleSearch}
                />
              </InputGroup>
            </Col>
            <Col lg={6} md={12} className="d-flex justify-content-lg-end">
              <div className="category-filters">
                {categories.map((category, index) => (
                  <Button
                    key={index}
                    variant={selectedCategory === category ? 'primary' : 'outline-primary'}
                    className="category-filter-btn me-2 mb-2"
                    onClick={() => handleCategoryChange(category)}
                  >
                    {category}
                  </Button>
                ))}
                <Button
                  variant="outline-secondary"
                  className="sort-btn ms-2"
                  onClick={toggleSortOrder}
                  title={sortOrder === 'desc' ? 'Newest first' : 'Oldest first'}
                >
                  {sortOrder === 'desc' ? <FaSortAmountDown /> : <FaSortAmountUp />}
                </Button>
              </div>
            </Col>
          </Row>
        </div>

        <div className="projects-count mb-4 text-left">
          <p>{filteredProjects.length} projects found</p>
        </div>

        <Row>
          {filteredProjects.map((project) => (
            <Col md={6} lg={4} key={project.id} className="mb-4">
              <ProjectCard project={project} />
            </Col>
          ))}
        </Row>

        {filteredProjects.length === 0 && (
          <div className="no-projects text-center py-5">
            <h3>No projects found</h3>
            <p>Try adjusting your search or filter criteria.</p>
          </div>
        )}
      </Container>
    </section>
  );
};

export default Projects;

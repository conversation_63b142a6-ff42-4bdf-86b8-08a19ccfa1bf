import React, { useState, useEffect } from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import axios from 'axios';
import SkillBar from '../components/SkillBar';
import './About.css';

const About = () => {
  const [skills, setSkills] = useState([]);
  const [experience, setExperience] = useState([]);
  const [education, setEducation] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Fetch data from API
    const fetchData = async () => {
      try {
        const [skillsRes, expRes, eduRes] = await Promise.all([
          axios.get('/api/skills'),
          axios.get('/api/experience'),
          axios.get('/api/education')
        ]);

        setSkills(skillsRes.data);
        setExperience(expRes.data);
        setEducation(eduRes.data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again later.');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <Container className="py-5 mt-5">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading about information...</p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="py-5 mt-5">
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      </Container>
    );
  }

  return (
    <div className="mt-5 pt-4">
      <section className="hero-about-section">
        <Container>
          <div className="hero-about-content">
            {/* Hero Introduction */}
            <div className="hero-intro text-center mb-5">
              <div className="hero-avatar-container">
                <div className="hero-avatar">
                  <span className="avatar-text">RB</span>
                  <div className="avatar-ring"></div>
                  <div className="avatar-pulse"></div>
                </div>
              </div>
              <h1 className="hero-title">
                <span className="hero-name">Rahul Bhoyar</span>
                <span className="hero-subtitle text-gradient">AI & Software Professional</span>
              </h1>
              <p className="hero-tagline">
                Transforming ideas into intelligent solutions with 7+ years of expertise in AI, Machine Learning, and Full-Stack Development
              </p>
            </div>

            {/* Key Highlights Cards */}
            <Row className="mb-5">
              <Col lg={4} md={6} className="mb-4">
                <div className="highlight-card">
                  <div className="highlight-icon">
                    <span>🤖</span>
                  </div>
                  <h4>AI Innovation</h4>
                  <p>Leading-edge work in Agentic AI, GenAI, and Deep Learning at DFKI Berlin</p>
                </div>
              </Col>
              <Col lg={4} md={6} className="mb-4">
                <div className="highlight-card">
                  <div className="highlight-icon">
                    <span>🚀</span>
                  </div>
                  <h4>Full-Stack Mastery</h4>
                  <p>End-to-end development with Python, React, FastAPI, and cloud technologies</p>
                </div>
              </Col>
              <Col lg={4} md={6} className="mb-4">
                <div className="highlight-card">
                  <div className="highlight-icon">
                    <span>🎯</span>
                  </div>
                  <h4>Impact Driven</h4>
                  <p>75+ projects delivered across education, healthcare, e-commerce, and research</p>
                </div>
              </Col>
            </Row>

            {/* Professional Journey */}
            <div className="journey-section mb-5">
              <h3 className="section-subtitle text-center mb-4">Professional Journey</h3>
              <Row className="align-items-center">
                <Col lg={6} className="mb-4 mb-lg-0">
                  <div className="journey-content">
                    <div className="journey-item">
                      <div className="journey-marker current"></div>
                      <div className="journey-details">
                        <h5>Research Associate/AI Engineer</h5>
                        <p className="journey-company">DFKI, Berlin</p>
                        <p className="journey-description">
                          Leading transformative AI projects in education, research, e-commerce, and healthcare.
                          Specializing in Agentic AI and Generative AI solutions.
                        </p>
                      </div>
                    </div>
                    <div className="journey-item">
                      <div className="journey-marker"></div>
                      <div className="journey-details">
                        <h5>Multi-Role Expertise</h5>
                        <p className="journey-company">Various Organizations</p>
                        <p className="journey-description">
                          Python Full Stack Developer, Data Scientist/Engineer, ML/AI Engineer -
                          bringing versatile expertise across the technology stack.
                        </p>
                      </div>
                    </div>
                  </div>
                </Col>
                <Col lg={6}>
                  <div className="tech-ecosystem">
                    <h5 className="text-center mb-3">Technology Ecosystem</h5>
                    <div className="tech-categories">
                      <div className="tech-category">
                        <span className="category-label">AI/ML</span>
                        <div className="tech-tags">
                          <span className="tech-tag">LangChain</span>
                          <span className="tech-tag">LangGraph</span>
                          <span className="tech-tag">LlamaIndex</span>
                          <span className="tech-tag">TensorFlow</span>
                          <span className="tech-tag">PyTorch</span>
                        </div>
                      </div>
                      <div className="tech-category">
                        <span className="category-label">Backend</span>
                        <div className="tech-tags">
                          <span className="tech-tag">Python</span>
                          <span className="tech-tag">FastAPI</span>
                          <span className="tech-tag">Django</span>
                          <span className="tech-tag">Flask</span>
                        </div>
                      </div>
                      <div className="tech-category">
                        <span className="category-label">Frontend</span>
                        <div className="tech-tags">
                          <span className="tech-tag">React</span>
                          <span className="tech-tag">JavaScript</span>
                          <span className="tech-tag">HTML/CSS</span>
                        </div>
                      </div>
                      <div className="tech-category">
                        <span className="category-label">Cloud</span>
                        <div className="tech-tags">
                          <span className="tech-tag">AWS</span>
                          <span className="tech-tag">GCP</span>
                          <span className="tech-tag">Azure</span>
                          <span className="tech-tag">Docker</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </Col>
              </Row>
            </div>

            {/* Achievement Stats */}
            <div className="achievement-stats">
              <Row>
                <Col md={3} sm={6} className="mb-3">
                  <div className="stat-card">
                    <div className="stat-icon">📈</div>
                    <div className="stat-number">7+</div>
                    <div className="stat-label">Years Experience</div>
                  </div>
                </Col>
                <Col md={3} sm={6} className="mb-3">
                  <div className="stat-card">
                    <div className="stat-icon">🎯</div>
                    <div className="stat-number">75+</div>
                    <div className="stat-label">Projects Delivered</div>
                  </div>
                </Col>
                <Col md={3} sm={6} className="mb-3">
                  <div className="stat-card">
                    <div className="stat-icon">⚡</div>
                    <div className="stat-number">20+</div>
                    <div className="stat-label">Tech Stack Mastery</div>
                  </div>
                </Col>
                <Col md={3} sm={6} className="mb-3">
                  <div className="stat-card">
                    <div className="stat-icon">🌍</div>
                    <div className="stat-number">2</div>
                    <div className="stat-label">Languages (EN/DE)</div>
                  </div>
                </Col>
              </Row>
            </div>
          </div>
        </Container>
      </section>

      <section className="skills-section bg-gradient-dark">
        <Container>
          <h2 className="section-title text-center">Technical Skills</h2>
          <div className="skills-intro text-center mb-5">
            <p className="lead">
              My expertise spans across multiple domains, with a focus on full-stack development and AI technologies
            </p>
          </div>
          <Row>
            {skills.map((skill, index) => (
              <Col md={6} key={index} className="mb-4">
                <SkillBar skill={skill} />
              </Col>
            ))}
          </Row>
          <div className="skills-categories mt-5">
            <Row>
              <Col md={6} className="mb-4">
                <div className="skill-category-card text-left">
                  <h4 className="text-accent mb-3">🤖 Agentic AI and Generative AI</h4>
                  <ul className="skill-list">
                    <li>LangGraph</li>
                    <li>LangChain</li>
                    <li>Phidata</li>
                    <li>LlamaIndex</li>
                    <li>CrewAI</li>
                  </ul>
                </div>
              </Col>
              <Col md={6} className="mb-4">
                <div className="skill-category-card text-left">
                  <h4 className="text-accent mb-3">🧠 Deep Learning and Machine Learning</h4>
                  <ul className="skill-list">
                    <li>Predictive Modeling</li>
                    <li>Natural Language Processing</li>
                    <li>Computer Vision</li>
                    <li>TensorFlow & PyTorch</li>
                    <li>MLOps & Model Deployment</li>
                  </ul>
                </div>
              </Col>
              <Col md={6} className="mb-4">
                <div className="skill-category-card text-left">
                  <h4 className="text-secondary mb-3">⚙️ Backend Development</h4>
                  <ul className="skill-list">
                    <li>Python & frameworks (FastAPI, Django, Flask)</li>
                    <li>RESTful API Design</li>
                    <li>Database Design</li>
                    <li>Authentication & Security</li>
                    <li>Microservices Architecture</li>
                  </ul>
                </div>
              </Col>
              <Col md={6} className="mb-4">
                <div className="skill-category-card text-left">
                  <h4 className="text-primary mb-3">🎨 Frontend Development</h4>
                  <ul className="skill-list">
                    <li>React.js & Redux</li>
                    <li>JavaScript/TypeScript</li>
                    <li>HTML5 & CSS3</li>
                    <li>Responsive Design</li>
                    <li>UI/UX Principles</li>
                  </ul>
                </div>
              </Col>
            </Row>
          </div>
        </Container>
      </section>

      <section className="section">
        <Container>
          <h2 className="section-title text-center">Professional Experience</h2>
          <div className="section-intro text-center mb-5">
            <p className="lead">
              A track record of delivering impactful solutions across various industries
            </p>
          </div>
          <Row>
            <Col lg={8} className="mx-auto">
              <div className="timeline">
                {experience.map((exp, index) => (
                  <div className="experience-card mb-4" key={index}>
                    <div className="card-body text-left">
                      <div className="experience-header">
                        <h3 className="experience-position">{exp.position}</h3>
                        <div className="experience-duration">
                          {exp.start_date} - {exp.end_date || 'Present'}
                          {exp.location && (
                            <span className="location-text"> • {exp.location}</span>
                          )}
                        </div>
                      </div>
                      <div className="experience-company mb-3">
                        <div className="company-info">
                          {exp.logo && (
                            <img
                              src={exp.logo}
                              alt={`${exp.company} logo`}
                              className="company-logo-square"
                              onError={(e) => {
                                e.target.style.display = 'none';
                                e.target.nextSibling.style.display = 'block';
                              }}
                            />
                          )}
                          <span className="company-logo-fallback-square" style={{display: exp.logo ? 'none' : 'block'}}>
                            {exp.company.charAt(0).toUpperCase()}
                          </span>
                          <div className="company-details">
                            <span className="company-name">{exp.company}</span>
                            {exp.company_url && (
                              <a
                                href={exp.company_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="company-url"
                              >
                                {exp.company_url}
                              </a>
                            )}
                          </div>
                        </div>
                      </div>
                      <p className="experience-description">{exp.description}</p>
                      <div className="experience-technologies">
                        {exp.technologies.map((tech, i) => (
                          <span className="tech-badge" key={i}>
                            {tech}
                          </span>
                        ))}
                      </div>
                      <div className="experience-achievements mt-3">
                        <h5 className="achievements-title">Key Achievements:</h5>
                        <ul className="achievements-list">
                          {exp.achievements && exp.achievements.map((achievement, i) => (
                            <li key={i}>{achievement}</li>
                          ))}
                          {!exp.achievements && (
                            <>
                              <li>Successfully delivered projects on time and within budget</li>
                              <li>Collaborated effectively with cross-functional teams</li>
                              <li>Implemented best practices and improved development workflows</li>
                            </>
                          )}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      <section className="section bg-gradient-dark">
        <Container>
          <h2 className="section-title text-center">Education</h2>
          <div className="section-intro text-center mb-5">
            <p className="lead">
              Academic background and formal education
            </p>
          </div>
          <Row>
            <Col lg={8} className="mx-auto">
              <div className="timeline">
                {education.map((edu, index) => (
                  <div className="education-card mb-4" key={index}>
                    <div className="card-body text-left">
                      <div className="education-header">
                        <h3 className="education-degree">{edu.degree} in {edu.field}</h3>
                        <div className="education-duration">
                          {edu.start_date} - {edu.end_date}
                          {edu.location && (
                            <span className="location-text"> • {edu.location}</span>
                          )}
                        </div>
                      </div>
                      <div className="education-institution mb-3">
                        <div className="institution-info">
                          {edu.logo && (
                            <img
                              src={edu.logo}
                              alt={`${edu.institution} logo`}
                              className="institution-logo-square"
                              onError={(e) => {
                                e.target.style.display = 'none';
                                e.target.nextSibling.style.display = 'block';
                              }}
                            />
                          )}
                          <span className="institution-logo-fallback-square" style={{display: edu.logo ? 'none' : 'block'}}>
                            {edu.institution.charAt(0).toUpperCase()}
                          </span>
                          <div className="institution-details">
                            <span className="institution-name">{edu.institution}</span>
                            {edu.institution_url && (
                              <a
                                href={edu.institution_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="institution-url"
                              >
                                {edu.institution_url}
                              </a>
                            )}
                          </div>
                        </div>
                      </div>
                      {edu.description && <p className="education-description">{edu.description}</p>}
                      {edu.achievements && (
                        <div className="education-achievements">
                          <h5 className="achievements-title">Highlights:</h5>
                          <ul className="achievements-list">
                            {edu.achievements.map((achievement, i) => (
                              <li key={i}>{achievement}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </Col>
          </Row>


        </Container>
      </section>
    </div>
  );
};

export default About;

/* Hero Section */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: 5rem 0 0 0;
  overflow: hidden;
  background-color: var(--bg-primary);
}

.hero-container {
  padding: 0;
  max-width: 100%;
  position: relative;
}

.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 80% 20%, rgba(128, 0, 255, 0.15), transparent 50%),
    radial-gradient(circle at 20% 80%, rgba(0, 128, 255, 0.1), transparent 50%);
  opacity: 0.8;
  z-index: -1;
}

.hero-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(rgba(255, 255, 255, 0.15) 2px, transparent 2px),
    radial-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px, 25px 25px;
  background-position: 0 0, 25px 25px;
  animation: particleAnimation 20s linear infinite;
}

@keyframes particleAnimation {
  0% {
    background-position: 0 0, 25px 25px;
  }
  100% {
    background-position: 50px 50px, 75px 75px;
  }
}

.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  text-align: center;
}

.scroll-icon {
  color: var(--text-primary);
  font-size: 2rem;
  opacity: 0.7;
}

.bounce {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}

.hero-content {
  padding: 6rem 4rem;
  position: relative;
  z-index: 2;
  opacity: 0;
  transform: translateY(20px);
}

.fade-in {
  animation: fadeIn 1s ease-out forwards;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-image-col {
  padding: 0;
  height: 100vh;
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateX(50px);
}

.slide-in-right {
  animation: slideInRight 1s ease-out forwards;
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.hero-image-container {
  height: 100%;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.5s ease;
}

.hero-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, var(--bg-primary) 0%, transparent 100%);
  z-index: 1;
}

.social-links-hero {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 2rem;
  margin-bottom: 1rem;
  padding: 0.5rem;
  border-radius: 12px;
  background-color: rgba(30, 30, 30, 0.3);
  max-width: 90%;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  min-width: 44px; /* Touch target minimum */
  min-height: 44px;
  border-radius: 50%;
  background-color: rgba(30, 30, 30, 0.7);
  color: var(--text-primary);
  font-size: 1.4rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  margin-right: 0.75rem;
  margin-bottom: 0.5rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  -webkit-tap-highlight-color: transparent; /* Remove iOS tap highlight */
}

.social-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

/* Social link background gradients */
.social-link.github::before {
  background: linear-gradient(135deg, var(--md-purple), var(--md-purple-variant));
}

.social-link.linkedin::before {
  background: linear-gradient(135deg, var(--md-blue), var(--md-blue-variant));
}

.social-link.twitter::before {
  background: linear-gradient(135deg, var(--md-teal), var(--md-teal-variant));
}

.social-link.kaggle::before {
  background: linear-gradient(135deg, #20beff, #0073b7);
}

.social-link.huggingface::before {
  background: linear-gradient(135deg, #ffbd59, #ff8c00);
}

.social-link.medium::before {
  background: linear-gradient(135deg, #12100e, #333333);
}

.social-link.google-scholar::before {
  background: linear-gradient(135deg, #4285f4, #0d47a1);
}

.social-link.dockerhub::before {
  background: linear-gradient(135deg, #099cec, #066da5);
}

.social-link.researchgate::before {
  background: linear-gradient(135deg, #00ccbb, #00a396);
}

.social-link.figma::before {
  background: linear-gradient(135deg, #ff7262, #f24e1e);
}

/* Social link hover effects */
.social-link.github:hover {
  color: white;
  border-color: var(--md-purple);
  box-shadow: 0 0 15px rgba(187, 134, 252, 0.5);
}

.social-link.linkedin:hover {
  color: white;
  border-color: var(--md-blue);
  box-shadow: 0 0 15px rgba(100, 181, 246, 0.5);
}

.social-link.twitter:hover {
  color: white;
  border-color: var(--md-teal);
  box-shadow: 0 0 15px rgba(3, 218, 198, 0.5);
}

.social-link.kaggle:hover {
  color: white;
  border-color: #20beff;
  box-shadow: 0 0 15px rgba(32, 190, 255, 0.5);
}

.social-link.huggingface:hover {
  color: white;
  border-color: #ffbd59;
  box-shadow: 0 0 15px rgba(255, 189, 89, 0.5);
}

.social-link.medium:hover {
  color: white;
  border-color: #12100e;
  box-shadow: 0 0 15px rgba(18, 16, 14, 0.5);
}

.social-link.google-scholar:hover {
  color: white;
  border-color: #4285f4;
  box-shadow: 0 0 15px rgba(66, 133, 244, 0.5);
}

.social-link.dockerhub:hover {
  color: white;
  border-color: #099cec;
  box-shadow: 0 0 15px rgba(9, 156, 236, 0.5);
}

.social-link.researchgate:hover {
  color: white;
  border-color: #00ccbb;
  box-shadow: 0 0 15px rgba(0, 204, 187, 0.5);
}

.social-link.figma:hover {
  color: white;
  border-color: #ff7262;
  box-shadow: 0 0 15px rgba(255, 114, 98, 0.5);
}

.social-link:hover {
  transform: translateY(-8px) scale(1.1);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.social-link:hover::before {
  opacity: 1;
}

.hero-intro {
  margin-bottom: 2.5rem;
}

.badge-container {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  margin-right: 0.75rem;
  margin-bottom: 0.75rem;
  background-color: rgba(128, 0, 255, 0.15);
  color: #ffffff;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  box-shadow: 0 4px 15px rgba(128, 0, 255, 0.2);
  transition: all 0.3s ease;
  border: 1px solid rgba(128, 0, 255, 0.3);
  backdrop-filter: blur(5px);
}

.badge-animate {
  opacity: 0;
  transform: translateY(20px);
  animation: badgeAnimation 0.6s ease-out forwards;
}

@keyframes badgeAnimation {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.badge:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 20px rgba(128, 0, 255, 0.3);
  background-color: rgba(128, 0, 255, 0.25);
  color: white;
}

.hero-headline {
  margin-bottom: 1.5rem;
}

.hero-greeting {
  font-size: 2.5rem;
  font-weight: 600;
  line-height: 1.1;
  margin-bottom: 1rem;
  color: var(--text-primary);
  letter-spacing: -0.02em;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.greeting-text {
  font-size: 2rem;
  font-weight: 500;
  color: var(--text-secondary);
  opacity: 0.9;
}

.greeting-highlight {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--md-purple), var(--md-teal));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% {
    background: linear-gradient(135deg, var(--md-purple), var(--md-teal));
    -webkit-background-clip: text;
    background-clip: text;
  }
  50% {
    background: linear-gradient(135deg, var(--md-teal), var(--md-purple));
    -webkit-background-clip: text;
    background-clip: text;
  }
}

.hero-name {
  font-size: 4.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 0.75rem;
  letter-spacing: -0.02em;
}

.hero-subtitle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.subtitle-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--md-purple);
  background: rgba(187, 134, 252, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 25px;
  border: 1px solid rgba(187, 134, 252, 0.2);
  transition: all 0.3s ease;
  animation: subtitleFloat 3s ease-in-out infinite;
}

.subtitle-text:nth-child(1) { animation-delay: 0s; }
.subtitle-text:nth-child(3) { animation-delay: 0.5s; }
.subtitle-text:nth-child(5) { animation-delay: 1s; }

@keyframes subtitleFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-3px); }
}

.subtitle-text:hover {
  background: rgba(187, 134, 252, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(187, 134, 252, 0.3);
}

.subtitle-separator {
  color: var(--text-tertiary);
  font-weight: 300;
  font-size: 1.2rem;
}

.text-gradient {
  background: linear-gradient(135deg, #bb86fc, #03dac6);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  display: inline-block;
}

.text-gradient::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #bb86fc, #03dac6);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  opacity: 0.5;
  filter: blur(10px);
  z-index: -1;
}

.hero-tagline-container {
  margin-bottom: 2rem;
  max-width: 540px;
}

.hero-tagline {
  font-size: 1.4rem;
  line-height: 1.6;
  margin-bottom: 0.75rem;
  color: var(--text-secondary);
  font-weight: 400;
  max-width: 600px;
}

.highlight {
  position: relative;
  color: var(--md-teal);
  font-weight: 600;
  padding: 0.1rem 0.3rem;
  border-radius: 4px;
  background: rgba(3, 218, 198, 0.1);
  border: 1px solid rgba(3, 218, 198, 0.2);
  transition: all 0.3s ease;
}

.highlight::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, var(--md-teal), var(--md-purple));
  z-index: -1;
  border-radius: 1px;
  opacity: 0.6;
}

.pulse {
  animation: highlightPulse 3s ease-in-out infinite;
}

@keyframes highlightPulse {
  0%, 100% {
    background: rgba(3, 218, 198, 0.1);
    border-color: rgba(3, 218, 198, 0.2);
    transform: scale(1);
  }
  50% {
    background: rgba(3, 218, 198, 0.2);
    border-color: rgba(3, 218, 198, 0.4);
    transform: scale(1.02);
  }
}

@keyframes pulse {
  0% {
    text-shadow: 0 0 0 rgba(187, 134, 252, 0);
  }
  50% {
    text-shadow: 0 0 10px rgba(187, 134, 252, 0.5);
  }
  100% {
    text-shadow: 0 0 0 rgba(187, 134, 252, 0);
  }
}

.hero-specialties {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-secondary);
  font-weight: 400;
}

.specialty {
  color: var(--md-teal);
  font-weight: 600;
  transition: color 0.3s ease;
}

.specialty:hover {
  color: var(--md-purple);
}

.hero-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 1.25rem;
  margin-bottom: 2rem;
}

.cta-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.75rem;
  min-height: 44px; /* Touch target minimum */
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  letter-spacing: 0.5px;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  overflow: hidden;
  border: none;
  z-index: 1;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  text-decoration: none;
}

.cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(-100%);
  transition: transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  z-index: -1;
}

.cta-button:hover::before {
  transform: translateX(0);
}

.cta-text {
  position: relative;
  z-index: 2;
}

.cta-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 0.75rem;
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease;
}

.primary-cta {
  background: linear-gradient(135deg, #bb86fc, #03dac6);
  color: white;
  box-shadow: 0 4px 15px rgba(187, 134, 252, 0.3);
}

.primary-cta:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(187, 134, 252, 0.4);
  color: white;
  background: linear-gradient(135deg, #9a66ea, #00a896);
}

.primary-cta:hover .cta-icon {
  transform: translateX(5px);
}

.secondary-cta {
  background: transparent;
  color: #bb86fc;
  border: 2px solid #bb86fc;
}

.secondary-cta:hover {
  background-color: rgba(187, 134, 252, 0.1);
  color: #bb86fc;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(187, 134, 252, 0.2);
}

.hero-code-container {
  position: relative;
  animation: float 6s ease-in-out infinite;
  max-width: 500px;
  margin: 0 auto;
}

.code-block {
  background-color: var(--bg-secondary);
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  overflow: hidden;
  font-family: var(--font-family-mono);
  font-size: 14px;
  line-height: 1.5;
}

.code-header {
  background-color: var(--bg-tertiary);
  padding: 10px 15px;
  display: flex;
  align-items: center;
}

.code-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 6px;
  background-color: var(--accent-orange);
}

.code-dot:nth-child(2) {
  background-color: var(--accent-teal);
}

.code-dot:nth-child(3) {
  background-color: var(--secondary-500);
}

.code-title {
  margin-left: 10px;
  font-size: 12px;
  color: var(--text-secondary);
}

.code-content {
  padding: 15px;
  color: var(--text-primary);
  overflow-x: auto;
}

.code-content pre {
  margin: 0;
  color: var(--text-primary);
}

/* Syntax highlighting */
.code-content .keyword {
  color: var(--primary-500);
}

.code-content .string {
  color: var(--secondary-500);
}

.code-content .function {
  color: var(--accent-purple);
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Services Section */
.services-section {
  padding: 100px 0;
  background-color: var(--bg-secondary);
  position: relative;
  overflow: hidden;
}

.services-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 10% 10%, rgba(187, 134, 252, 0.05), transparent 30%),
    radial-gradient(circle at 90% 90%, rgba(3, 218, 198, 0.05), transparent 30%);
  z-index: 0;
}

.services-section.animate-in .service-col {
  animation: fadeInUp 0.6s ease-out forwards;
}

.service-col {
  opacity: 0;
  transform: translateY(30px);
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
  z-index: 1;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  color: var(--text-primary);
  position: relative;
  display: inline-block;
}

.section-title-underline {
  width: 80px;
  height: 4px;
  background: linear-gradient(135deg, #bb86fc, #03dac6);
  margin: 0 auto;
  border-radius: 2px;
}

.service-card {
  background-color: rgba(30, 30, 30, 0.6);
  border-radius: 16px;
  padding: 2rem;
  height: 100%;
  transition: all 0.4s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  z-index: 1;
  border: 1px solid rgba(187, 134, 252, 0.1);
  backdrop-filter: blur(10px);
}

.service-card-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(187, 134, 252, 0.05), rgba(3, 218, 198, 0.05));
  z-index: -1;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(187, 134, 252, 0.3);
}

.service-icon-container {
  margin-bottom: 1.5rem;
  position: relative;
}

.service-icon {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: #bb86fc;
  background: rgba(187, 134, 252, 0.1);
  border-radius: 16px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.service-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(187, 134, 252, 0.2), rgba(3, 218, 198, 0.2));
  border-radius: 16px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.service-card:hover .service-icon {
  color: white;
  transform: rotate(10deg) scale(1.1);
}

.service-card:hover .service-icon::before {
  opacity: 1;
}

.service-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.service-description {
  flex-grow: 1;
  margin-bottom: 1.5rem;
  color: var(--text-secondary);
}

.service-link {
  display: inline-flex;
  align-items: center;
  color: #bb86fc;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
}

.service-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #bb86fc, #03dac6);
  transition: width 0.3s ease;
}

.service-link:hover::after {
  width: 100%;
}

.service-link:hover {
  color: #bb86fc;
  transform: translateX(5px);
}

/* Stats Section */
.stats-section {
  padding: 80px 0;
  background: #121212;
  color: white;
  position: relative;
  overflow: hidden;
}

.stats-section.animate-in .stat-item {
  animation: fadeInUp 0.6s ease-out forwards;
}

.stats-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(187, 134, 252, 0.1), rgba(3, 218, 198, 0.1));
  z-index: 0;
}

.stats-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 50%, rgba(187, 134, 252, 0.15), transparent 40%),
    radial-gradient(circle at 80% 50%, rgba(3, 218, 198, 0.15), transparent 40%);
  z-index: 0;
}

.stat-item {
  text-align: center;
  padding: 2rem 1rem;
  position: relative;
  z-index: 1;
  opacity: 0;
  transform: translateY(30px);
}

.stat-card {
  background-color: rgba(30, 30, 30, 0.6);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(187, 134, 252, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(187, 134, 252, 0.3);
}

.stat-icon {
  font-size: 2.5rem;
  color: #bb86fc;
  margin-bottom: 1rem;
}

.stat-number {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #bb86fc, #03dac6);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stat-label {
  font-size: 1.25rem;
  font-weight: 500;
  opacity: 0.9;
  color: #e0e0e0;
}

/* Responsive Design */
@media (max-width: 992px) {
  .greeting-text {
    font-size: 1.75rem;
  }

  .greeting-highlight {
    font-size: 2rem;
  }

  .hero-name {
    font-size: 3.5rem;
  }

  .hero-tagline {
    font-size: 1.25rem;
  }

  .subtitle-text {
    font-size: 1rem;
    padding: 0.4rem 0.8rem;
  }

  .hero-subtitle {
    gap: 0.5rem;
  }

  .hero-specialties {
    font-size: 1rem;
  }

  .hero-content {
    padding: 4rem 2rem;
  }

  .badge-container {
    justify-content: flex-start;
  }

  .hero-tagline-container {
    margin-right: auto;
  }

  .hero-buttons {
    justify-content: flex-start;
    flex-direction: column;
    align-items: flex-start;
  }

  .cta-button {
    width: 100%;
    max-width: 300px;
    margin-bottom: 0.75rem;
  }

  .social-links-hero {
    justify-content: flex-start;
    max-width: 100%;
  }

  .hero-image-col {
    height: 50vh;
  }

  .hero-image-overlay {
    background: linear-gradient(0deg, var(--bg-primary) 0%, transparent 100%);
  }
}

@media (max-width: 768px) {
  .hero-section {
    flex-direction: column;
    padding: 4rem 0 0 0;
  }

  .hero-content {
    padding: 5rem 1.5rem 2rem;
    order: 2;
    text-align: center;
  }

  .hero-image-col {
    order: 1;
    height: 40vh;
  }

  .greeting-text {
    font-size: 1.5rem;
  }

  .greeting-highlight {
    font-size: 1.75rem;
  }

  .hero-name {
    font-size: 2.75rem;
  }

  .hero-tagline {
    font-size: 1.125rem;
  }

  .subtitle-text {
    font-size: 0.9rem;
    padding: 0.3rem 0.6rem;
  }

  .hero-subtitle {
    justify-content: center;
    gap: 0.4rem;
  }

  .hero-buttons {
    justify-content: center;
    align-items: center;
  }

  .cta-button {
    width: 100%;
    max-width: 280px;
    margin-bottom: 0.75rem;
  }

  .social-links-hero {
    justify-content: center;
    max-width: 100%;
  }

  .services-section {
    padding: 60px 0;
  }

  .service-card {
    margin-bottom: 2rem;
  }

  .stat-item {
    margin-bottom: 2rem;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .stat-label {
    font-size: 1rem;
  }
}

/* Mobile devices */
@media (max-width: 576px) {
  .hero-section {
    padding: 3rem 0 0 0;
  }

  .hero-content {
    padding: 4rem 1rem 2rem;
  }

  .hero-greeting {
    font-size: 1.5rem;
  }

  .hero-name {
    font-size: 2.25rem;
    line-height: 1.2;
  }

  .hero-tagline {
    font-size: 1rem;
  }

  .hero-specialties {
    font-size: 0.9rem;
  }

  .badge {
    font-size: 0.7rem;
    padding: 0.4rem 0.8rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .social-link {
    width: 45px;
    height: 45px;
    font-size: 1.2rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .cta-button {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }

  .hero-image-col {
    height: 35vh;
  }

  .services-section {
    padding: 40px 0;
  }

  .section-title {
    font-size: 2rem;
  }

  .service-card {
    padding: 1.5rem;
  }

  .service-icon {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .stat-label {
    font-size: 0.9rem;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .hero-content {
    padding: 3rem 0.75rem 1.5rem;
  }

  .hero-greeting {
    font-size: 1.25rem;
  }

  .hero-name {
    font-size: 1.875rem;
  }

  .hero-tagline {
    font-size: 0.9rem;
  }

  .badge {
    font-size: 0.65rem;
    padding: 0.3rem 0.6rem;
  }

  .social-link {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .cta-button {
    padding: 0.625rem 1.25rem;
    font-size: 0.85rem;
  }

  .service-card {
    padding: 1.25rem;
  }

  .service-icon {
    width: 50px;
    height: 50px;
    font-size: 1.75rem;
  }

  .service-title {
    font-size: 1.25rem;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Disable hover effects on touch devices */
  .social-link:hover {
    transform: none;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .cta-button:hover {
    transform: none;
    box-shadow: 0 4px 15px rgba(187, 134, 252, 0.3);
  }

  .service-card:hover {
    transform: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  }

  .badge:hover {
    transform: none;
    box-shadow: 0 4px 15px rgba(128, 0, 255, 0.2);
  }

  /* Add active states for touch feedback */
  .social-link:active {
    transform: scale(0.95);
  }

  .cta-button:active {
    transform: scale(0.98);
  }

  .service-card:active {
    transform: scale(0.99);
  }

  /* Ensure touch targets are large enough */
  .social-link {
    min-width: 48px;
    min-height: 48px;
  }

  .cta-button {
    min-height: 48px;
    padding: 0.875rem 2rem;
  }

  .badge {
    min-height: 32px;
    padding: 0.5rem 1rem;
  }
}

/* Mobile devices */
@media (max-width: 576px) {
  .hero-section {
    padding: 3rem 0 0 0;
  }

  .hero-content {
    padding: 4rem 1rem 2rem;
  }

  .hero-greeting {
    font-size: 1.5rem;
  }

  .hero-name {
    font-size: 2.25rem;
    line-height: 1.2;
  }

  .hero-tagline {
    font-size: 1rem;
  }

  .hero-specialties {
    font-size: 0.9rem;
  }

  .badge {
    font-size: 0.7rem;
    padding: 0.4rem 0.8rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .social-link {
    width: 45px;
    height: 45px;
    font-size: 1.2rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .cta-button {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }

  .hero-image-col {
    height: 35vh;
  }

  .services-section {
    padding: 40px 0;
  }

  .section-title {
    font-size: 2rem;
  }

  .service-card {
    padding: 1.5rem;
  }

  .service-icon {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .stat-label {
    font-size: 0.9rem;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .hero-content {
    padding: 3rem 0.75rem 1.5rem;
  }

  .hero-greeting {
    font-size: 1.25rem;
  }

  .hero-name {
    font-size: 1.875rem;
  }

  .hero-tagline {
    font-size: 0.9rem;
  }

  .badge {
    font-size: 0.65rem;
    padding: 0.3rem 0.6rem;
  }

  .social-link {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .cta-button {
    padding: 0.625rem 1.25rem;
    font-size: 0.85rem;
  }

  .service-card {
    padding: 1.25rem;
  }

  .service-icon {
    width: 50px;
    height: 50px;
    font-size: 1.75rem;
  }

  .service-title {
    font-size: 1.25rem;
  }
}

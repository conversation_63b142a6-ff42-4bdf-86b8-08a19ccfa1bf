/* Hero About Section */
.hero-about-section {
  padding: 3rem 0 1rem 0; /* Reduced bottom padding to decrease space before Technical Skills */
  background: linear-gradient(135deg, rgba(18, 18, 18, 0.95), rgba(37, 37, 37, 0.95));
  position: relative;
  overflow: hidden;
}

.hero-about-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(187, 134, 252, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(3, 218, 198, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.hero-about-content {
  position: relative;
  z-index: 1;
}

/* Hero Introduction */
.hero-intro {
  margin-bottom: 3rem;
}

.hero-avatar-container {
  position: relative;
  display: inline-block;
  margin-bottom: 2rem;
}

.hero-avatar {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.hero-avatar:hover {
  transform: scale(1.05) rotate(5deg);
  box-shadow: 0 25px 50px rgba(187, 134, 252, 0.3);
}

.avatar-text {
  font-size: 2.5rem;
  font-weight: 800;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.avatar-ring {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 2px solid rgba(187, 134, 252, 0.3);
  border-radius: 50%;
  animation: rotate 20s linear infinite;
}

.avatar-pulse {
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  border: 1px solid rgba(3, 218, 198, 0.2);
  border-radius: 50%;
  animation: pulse 3s ease-in-out infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.1; }
}

.hero-title {
  margin-bottom: 1.5rem;
}

.hero-name {
  display: block;
  font-size: 3.5rem;
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  line-height: 1.1;
}

.hero-subtitle {
  display: block;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.text-gradient {
  background: linear-gradient(135deg, var(--md-purple), var(--md-teal));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

.hero-tagline {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Highlight Cards */
.highlight-card {
  background: rgba(30, 30, 30, 0.7);
  border: 1px solid rgba(187, 134, 252, 0.1);
  border-radius: var(--radius-lg);
  padding: 2rem;
  text-align: center;
  height: 100%;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.highlight-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.highlight-card:hover::before {
  transform: scaleX(1);
}

.highlight-card:hover {
  transform: translateY(-10px);
  border-color: rgba(187, 134, 252, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.highlight-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.highlight-card h4 {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.highlight-card p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* Journey Section */
.journey-section {
  background: rgba(30, 30, 30, 0.4);
  border-radius: var(--radius-lg);
  padding: 2.5rem;
  border: 1px solid rgba(187, 134, 252, 0.1);
  backdrop-filter: blur(10px);
}

.section-subtitle {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 2rem;
}

.journey-content {
  position: relative;
}

.journey-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 2rem;
  position: relative;
}

.journey-item:last-child {
  margin-bottom: 0;
}

.journey-marker {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--md-purple);
  margin-right: 1.5rem;
  margin-top: 0.25rem;
  flex-shrink: 0;
  position: relative;
  box-shadow: 0 0 0 4px rgba(187, 134, 252, 0.2);
}

.journey-marker.current {
  background: var(--md-teal);
  box-shadow: 0 0 0 4px rgba(3, 218, 198, 0.2);
  animation: pulse-current 2s ease-in-out infinite;
}

@keyframes pulse-current {
  0%, 100% { box-shadow: 0 0 0 4px rgba(3, 218, 198, 0.2); }
  50% { box-shadow: 0 0 0 8px rgba(3, 218, 198, 0.1); }
}

.journey-details h5 {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.journey-company {
  font-size: 1rem;
  color: var(--md-teal);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.journey-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* Technology Ecosystem */
.tech-ecosystem {
  background: rgba(30, 30, 30, 0.6);
  border-radius: var(--radius-lg);
  padding: 2rem;
  border: 1px solid rgba(187, 134, 252, 0.1);
  backdrop-filter: blur(10px);
  height: 100%;
}

.tech-ecosystem h5 {
  color: var(--text-primary);
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.tech-categories {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.tech-category {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.category-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--md-purple);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tech-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tech-tag {
  display: inline-block;
  padding: 0.4rem 0.8rem;
  background: rgba(187, 134, 252, 0.1);
  color: var(--md-purple);
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid rgba(187, 134, 252, 0.2);
  transition: all 0.3s ease;
}

.tech-tag:hover {
  background: rgba(187, 134, 252, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(187, 134, 252, 0.2);
}

/* Achievement Stats */
.achievement-stats {
  margin-top: 3rem;
}

.stat-card {
  background: rgba(30, 30, 30, 0.7);
  border: 1px solid rgba(187, 134, 252, 0.1);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  height: 100%;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.stat-card:hover::before {
  transform: scaleX(1);
}

.stat-card:hover {
  transform: translateY(-5px);
  border-color: rgba(187, 134, 252, 0.3);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  display: block;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: block;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Skills Section */
.skills-section {
  padding-top: 2rem !important; /* Reduced from default section padding to decrease space */
}

.skills-intro {
  max-width: 700px;
  margin: 0 auto;
}

.skill-category-card {
  background-color: rgba(30, 30, 30, 0.6);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  height: 100%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(187, 134, 252, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.skill-category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(187, 134, 252, 0.3);
}

.skill-list {
  list-style-type: none;
  padding-left: 0;
  margin-bottom: 0;
}

.skill-list li {
  padding: 0.5rem 0;
  position: relative;
  padding-left: 1.5rem;
  color: var(--text-secondary);
}

.skill-list li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--md-purple);
  font-size: 1.2rem;
}

/* Experience Section */
.experience-card {
  background-color: rgba(30, 30, 30, 0.6);
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(187, 134, 252, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  padding: 1.5rem;
}

.experience-card:hover {
  transform: translateX(10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(187, 134, 252, 0.3);
}

.experience-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.experience-position {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  color: var(--md-purple);
}

.experience-duration {
  font-size: 0.9rem;
  color: var(--text-tertiary);
  background-color: rgba(187, 134, 252, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
}

.experience-company {
  display: flex;
  align-items: center;
}

.company-logo-container {
  margin-right: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 80px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 10px;
  border: 1px solid rgba(187, 134, 252, 0.2);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.company-logo {
  height: auto;
  max-height: 70px;
  max-width: 110px;
  object-fit: contain;
  transition: all 0.3s ease;
}

.experience-card:hover .company-logo-container {
  background-color: rgba(187, 134, 252, 0.1);
  border-color: rgba(187, 134, 252, 0.3);
  box-shadow: 0 5px 15px rgba(187, 134, 252, 0.1);
}

.experience-card:hover .company-logo {
  transform: rotate(5deg) scale(1.1);
  box-shadow: 0 0 15px rgba(187, 134, 252, 0.5);
}

.company-logo-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background-color: rgba(187, 134, 252, 0.1);
  border-radius: var(--radius-md);
  color: var(--md-purple);
  font-weight: 600;
  font-size: 1.5rem;
  transition: all 0.3s ease;
  text-align: center;
  line-height: 1.2;
}

.experience-card:hover .company-logo-fallback {
  background-color: var(--md-purple);
  color: white;
  transform: rotate(10deg) scale(1.1);
  box-shadow: 0 0 15px rgba(187, 134, 252, 0.5);
}

.company-name {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-secondary);
}

/* New squared logo styles */
.company-info {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.company-logo-square {
  width: 70px;
  height: 70px;
  object-fit: contain;
  border-radius: var(--radius-md);
  background-color: rgba(255, 255, 255, 0.95);
  padding: 10px;
  transition: all 0.3s ease;
  border: 1px solid rgba(187, 134, 252, 0.1);
  flex-shrink: 0;
  filter: brightness(1) contrast(1.1);
}

.experience-card:hover .company-logo-square {
  transform: rotate(5deg) scale(1.15);
  box-shadow: 0 0 15px rgba(187, 134, 252, 0.5);
  background-color: rgba(255, 255, 255, 1);
  filter: brightness(1.2) contrast(1.2);
}

.company-logo-fallback-square {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background-color: rgba(187, 134, 252, 0.1);
  border-radius: var(--radius-md);
  color: var(--md-purple);
  font-weight: 600;
  font-size: 1.5rem;
  transition: all 0.3s ease;
  text-align: center;
  line-height: 1.2;
  flex-shrink: 0;
}

.experience-card:hover .company-logo-fallback-square {
  background-color: var(--md-purple);
  color: white;
  transform: rotate(10deg) scale(1.1);
  box-shadow: 0 0 15px rgba(187, 134, 252, 0.5);
}

.company-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.company-url {
  font-size: 0.9rem;
  color: var(--primary-500);
  text-decoration: none;
  transition: all 0.3s ease;
}

.company-url:hover {
  color: var(--primary-400);
  text-decoration: underline;
}

.location-text {
  color: var(--text-muted);
  font-size: 0.9rem;
  font-weight: 500;
}

.experience-technologies {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;
}

.tech-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background-color: rgba(3, 218, 198, 0.1);
  color: var(--md-teal);
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.tech-badge:hover {
  background-color: rgba(3, 218, 198, 0.2);
  transform: translateY(-2px);
}

.achievements-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.achievements-list {
  padding-left: 1.5rem;
  margin-bottom: 0;
}

.achievements-list li {
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
}

.achievements-list li:last-child {
  margin-bottom: 0;
}

/* Education Section */
.education-card {
  background-color: rgba(30, 30, 30, 0.6);
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(187, 134, 252, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  padding: 1.5rem;
}

.education-card:hover {
  transform: translateX(10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(187, 134, 252, 0.3);
}

.education-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.education-degree {
  font-size: 1.3rem;
  font-weight: 700;
  margin: 0;
  color: var(--md-teal);
}

.education-duration {
  font-size: 0.9rem;
  color: var(--text-tertiary);
  background-color: rgba(3, 218, 198, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
}

.education-institution {
  display: flex;
  align-items: center;
}

.institution-logo-container {
  margin-right: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 80px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 10px;
  border: 1px solid rgba(3, 218, 198, 0.2);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.institution-logo {
  height: auto;
  max-height: 70px;
  max-width: 110px;
  object-fit: contain;
  transition: all 0.3s ease;
}

.education-card:hover .institution-logo-container {
  background-color: rgba(3, 218, 198, 0.1);
  border-color: rgba(3, 218, 198, 0.3);
  box-shadow: 0 5px 15px rgba(3, 218, 198, 0.1);
}

.education-card:hover .institution-logo {
  transform: rotate(5deg) scale(1.1);
  box-shadow: 0 0 15px rgba(3, 218, 198, 0.5);
}

.institution-logo-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background-color: rgba(3, 218, 198, 0.1);
  border-radius: var(--radius-md);
  color: var(--md-teal);
  font-weight: 600;
  font-size: 1.5rem;
  transition: all 0.3s ease;
  text-align: center;
  line-height: 1.2;
}

.education-card:hover .institution-logo-fallback {
  background-color: var(--md-teal);
  color: white;
  transform: rotate(10deg) scale(1.1);
  box-shadow: 0 0 15px rgba(3, 218, 198, 0.5);
}

.institution-name {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-secondary);
}

/* New squared logo styles for education */
.institution-info {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.institution-logo-square {
  width: 70px;
  height: 70px;
  object-fit: contain;
  border-radius: var(--radius-md);
  background-color: rgba(255, 255, 255, 0.95);
  padding: 10px;
  transition: all 0.3s ease;
  border: 1px solid rgba(3, 218, 198, 0.1);
  flex-shrink: 0;
  filter: brightness(1) contrast(1.1);
}

.education-card:hover .institution-logo-square {
  transform: rotate(5deg) scale(1.15);
  box-shadow: 0 0 15px rgba(3, 218, 198, 0.5);
  background-color: rgba(255, 255, 255, 1);
  filter: brightness(1.2) contrast(1.2);
}

.institution-logo-fallback-square {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background-color: rgba(3, 218, 198, 0.1);
  border-radius: var(--radius-md);
  color: var(--md-teal);
  font-weight: 600;
  font-size: 1.5rem;
  transition: all 0.3s ease;
  text-align: center;
  line-height: 1.2;
  flex-shrink: 0;
}

.education-card:hover .institution-logo-fallback-square {
  background-color: var(--md-teal);
  color: white;
  transform: rotate(10deg) scale(1.1);
  box-shadow: 0 0 15px rgba(3, 218, 198, 0.5);
}

.institution-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.institution-url {
  font-size: 0.9rem;
  color: var(--primary-500);
  text-decoration: none;
  transition: all 0.3s ease;
}

.institution-url:hover {
  color: var(--primary-400);
  text-decoration: underline;
}

/* Certifications */
.certification-card {
  background-color: rgba(30, 30, 30, 0.6);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  height: 100%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(187, 134, 252, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.certification-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(187, 134, 252, 0.3);
}

.certification-logo {
  margin-bottom: 1rem;
}

.cert-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 8px;
  background-color: rgba(187, 134, 252, 0.1);
  color: var(--md-purple);
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.certification-card:hover .cert-icon {
  background-color: var(--md-purple);
  color: white;
  transform: rotate(10deg) scale(1.1);
  box-shadow: 0 0 15px rgba(187, 134, 252, 0.5);
}

.certification-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.certification-issuer {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.certification-date {
  font-size: 0.8rem;
  color: var(--text-tertiary);
  margin-bottom: 1rem;
}

.certification-verification {
  margin-top: 0.5rem;
}

.verification-link {
  display: inline-block;
  padding: 0.4rem 1rem;
  background-color: rgba(3, 218, 198, 0.1);
  color: var(--md-teal);
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 1px solid rgba(3, 218, 198, 0.2);
}

.verification-link:hover {
  background-color: rgba(3, 218, 198, 0.2);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(3, 218, 198, 0.2);
}

/* Timeline */
.timeline {
  position: relative;
  padding-left: 1.5rem;
}

.timeline .card {
  position: relative;
  border-radius: var(--radius-lg);
  background-color: var(--bg-primary);
  transition: all 0.3s ease;
}

.timeline .card:hover {
  transform: translateX(10px);
  box-shadow: var(--shadow-md);
}

.timeline .card::before {
  content: '';
  position: absolute;
  left: -1.5rem;
  top: 1.5rem;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(0, 118, 255, 0.2);
}

.timeline::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 2px;
  height: 100%;
  background-color: var(--border-color);
}

@media (max-width: 992px) {
  .hero-name {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .hero-tagline {
    font-size: 1rem;
  }

  .journey-section {
    padding: 2rem;
  }

  .tech-ecosystem {
    margin-top: 2rem;
  }

  .experience-header, .education-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .experience-duration, .education-duration {
    margin-top: 0.5rem;
  }
}

@media (max-width: 768px) {
  .hero-about-section {
    padding: 2rem 0 1rem 0;
  }

  .hero-name {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-tagline {
    font-size: 0.95rem;
  }

  .hero-avatar {
    width: 100px;
    height: 100px;
  }

  .avatar-text {
    font-size: 2rem;
  }

  .highlight-card {
    padding: 1.5rem;
  }

  .journey-section {
    padding: 1.5rem;
  }

  .tech-ecosystem {
    padding: 1.5rem;
  }

  .tech-categories {
    gap: 1rem;
  }

  .achievement-stats {
    margin-top: 2rem;
  }

  .stat-card {
    padding: 1.25rem;
  }

  .experience-technologies, .tech-stack-icons {
    justify-content: flex-start;
  }

  .experience-card, .education-card {
    padding: 1.25rem;
  }

  .experience-position, .education-degree {
    font-size: 1.25rem;
  }

  .certification-card {
    margin-bottom: 1rem;
  }
}

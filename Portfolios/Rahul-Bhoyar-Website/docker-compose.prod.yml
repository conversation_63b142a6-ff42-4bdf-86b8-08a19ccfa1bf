services:
  # ollama:   # LLM Server
  #   image: ollama/ollama:latest
  #   container_name: ollama-server
  #   environment:
  #     - OLLAMA_HOST=0.0.0.0:11434
  #     - GIN_MODE=release
  #     - OLLAMA_KEEP_ALIVE=-1  # Keep the server running
  #   ports:
  #     - "11434:11434"
  #   volumes:
  #     - ollama_models:/root/.ollama/models
  #   networks:
  #     - portfolio-network

  # pgvector-db: # Vector Database Server
  #   image: pgvector/pgvector:pg16
  #   container_name: pgvector-container
  #   environment:
  #     - POSTGRES_USER=langchain
  #     - POSTGRES_PASSWORD=langchain
  #     - POSTGRES_DB=langchain
  #   ports:
  #     - "6024:5432"
  #   restart: always
  #   volumes:
  #     - pgvector_data:/var/lib/postgresql/data
  #   networks:
  #     - portfolio-network
  
  # nginx-temp:
  #   image: nginx:alpine
  #   container_name: nginx-temp
  #   volumes:
  #     - ./certbot/www:/usr/share/nginx/html
  #   ports:
  #     - "80:80"


  certbot:
    image: certbot/certbot
    container_name: certbot
    volumes:
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    entrypoint: >
      sh -c "certbot certonly --webroot -w /var/www/certbot
      --email <EMAIL>
      --agree-tos --no-eff-email
      -d rahulbhoyar.com -d www.rahulbhoyar.com"

  frontend:
    container_name: frontend-container
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    volumes:
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    networks:
      - portfolio-network


  backend: # FastAPI Backend
    container_name: backend-container
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    # No direct port exposure to host - only through nginx
    expose:
      - "8000"
    volumes:
      - ./backend:/app
    networks:
      - portfolio-network
    environment:
      - CORS_ORIGINS=http://localhost  
      - GROQ_API_KEY=${GROQ_API_KEY}
      - GMAIL=${GMAIL} 
      - GMAIL_PASSWORD=${GMAIL_PASSWORD} 
      - RECIPIENT_EMAIL_1=${RECIPIENT_EMAIL_1}   
    extra_hosts:
      - "host.docker.internal:host-gateway"

# volumes:
#   #ollama_models:
#   pgvector_data:
#     driver: local

networks:
  portfolio-network:
    driver: bridge

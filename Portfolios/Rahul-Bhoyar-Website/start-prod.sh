#!/bin/bash

# Stop any running containers
echo "Stopping any running containers..."
docker compose -f docker-compose.prod.yml down


##### Docker Model Runner LLM Models
# LLM_MODEL_NAME="ai/llama3.2:1B-Q8_0"

# # Check if LLM_MODEL_NAME was found
# if [ -z "$LLM_MODEL_NAME" ]; then
#     echo "Error: LLM_MODEL_NAME not found."
#     exit 1
# fi

# echo "Using LLM model: $LLM_MODEL_NAME"

# # Pull the Docker model
# echo "Pulling Docker model..."
# docker model pull $LLM_MODEL_NAME


# Build and start the containers in development mode
echo "Building and starting containers in development and detached mode..."
docker compose -f docker-compose.prod.yml up --build -d



set -e

##### Ollama LLM Models

# MODELS="mistral:7b"

# model_exists() {
#     docker compose -f docker-compose.prod.yml exec ollama ollama list | grep -q "$1"
# }

# for MODEL_NAME in $MODELS; do
#     if model_exists "$MODEL_NAME"; then
#         echo "Model $MODEL_NAME already exists."
#     else
#         echo "Pulling model $MODEL_NAME..."
#         docker compose -f docker-compose.prod.yml exec ollama ollama pull "$MODEL_NAME"
#     fi
# done
echo "All models are ready."
echo "Production environment is set up and running."
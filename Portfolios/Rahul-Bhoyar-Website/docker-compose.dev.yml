services:
  # ollama:   # LLM Server
  #   image: ollama/ollama:latest
  #   container_name: ollama-server
  #   environment:
  #     - OLLAMA_HOST=0.0.0.0:11434
  #     - GIN_MODE=release
  #     - OLLAMA_KEEP_ALIVE=-1  # Keep the server running
  #   ports:
  #     - "11434:11434"
  #   volumes:
  #     - ollama_models:/root/.ollama/models
  #   networks:
  #     - portfolio-network

  # pgvector-db: # Vector Database Server
  #   image: pgvector/pgvector:pg16
  #   container_name: pgvector-container
  #   environment:
  #     - POSTGRES_USER=langchain
  #     - POSTGRES_PASSWORD=langchain
  #     - POSTGRES_DB=langchain
  #   ports:
  #     - "6024:5432"
  #   restart: always
  #   volumes:
  #     - pgvector_data:/var/lib/postgresql/data
  #   networks:
  #     - portfolio-network

  backend: # FastAPI Backend
    container_name: backend-container
    build:
      context: ./backend
      dockerfile: Dockerfile
    expose:
      - "8000"
    volumes:
      - ./backend:/app
    networks:
      - portfolio-network
    environment:
      - CORS_ORIGINS=http://localhost
    restart: always

  frontend: # React Frontend
    container_name: frontend-container
    build:
      context: ./frontend
      dockerfile: Dockerfile
    # No direct port exposure to host - only through nginx
    expose:
      - "3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - portfolio-network
    environment:
      - REACT_APP_API_URL=/api  # Updated to use relative URL through nginx
      - CHOKIDAR_USEPOLLING=true  # Enable hot reloading in Docker
      - WDS_SOCKET_PORT=3000  # Required for hot reloading
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 10s
      timeout: 5s
      retries: 5

  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile.dev
    ports:
      - "80:80"
    depends_on:
      frontend:
        condition: service_healthy
    restart: always
    networks:
      - portfolio-network

#volumes:
  #ollama_models:
  # pgvector_data:
  #   driver: local

networks:
  portfolio-network:
    driver: bridge
